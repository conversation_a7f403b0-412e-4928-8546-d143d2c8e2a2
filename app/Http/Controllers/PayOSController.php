<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Models\PaymentTransaction;
use App\Models\User;
use PayOS\PayOS;

class PayOSController extends Controller
{
    private $payOS;

    public function __construct()
    {
        $clientId = config('payos.client_id');
        $apiKey = config('payos.api_key');
        $checksumKey = config('payos.checksum_key');

        // Only initialize PayOS if all required credentials are present
        if ($clientId && $apiKey && $checksumKey) {
            $this->payOS = new PayOS($clientId, $apiKey, $checksumKey);
        }
    }

    /**
     * Create a payment link for token purchase
     */
    public function createPaymentLink(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'package_type' => 'required|string|in:basic,standard,premium'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid package type',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            // Check if PayOS is properly configured
            if (!$this->payOS) {
                return response()->json([
                    'success' => false,
                    'message' => 'PayOS is not properly configured. Please check your environment variables.'
                ], 500);
            }

            $user = Auth::user();
            $packageType = $request->package_type;
            $packageConfig = config("payos.packages.{$packageType}");

            if (!$packageConfig) {
                return response()->json([
                    'success' => false,
                    'message' => 'Package not found'
                ], 404);
            }

            // Generate unique order code
            $orderCode = PaymentTransaction::generateOrderCode();

            // Create payment transaction record
            $transaction = PaymentTransaction::create([
                'user_id' => $user->id,
                'payos_order_code' => $orderCode,
                'package_type' => $packageType,
                'amount' => $packageConfig['amount'],
                'tokens' => $packageConfig['tokens'],
                'status' => PaymentTransaction::STATUS_PENDING,
                'description' => "Purchase {$packageConfig['name']} package - {$packageConfig['tokens']} tokens"
            ]);

            // Prepare PayOS payment data
            $paymentData = [
                "orderCode" => $orderCode,
                "amount" => $packageConfig['amount'],
                "description" => $transaction->description,
                "items" => [
                    [
                        'name' => $packageConfig['name'] . ' Token Package',
                        'price' => $packageConfig['amount'],
                        'quantity' => 1
                    ]
                ],
                "buyerName" => $user->name,
                "buyerEmail" => $user->email,
                "returnUrl" => config('payos.return_url'),
                "cancelUrl" => config('payos.cancel_url')
            ];

            // Create payment link with PayOS
            $response = $this->payOS->createPaymentLink($paymentData);

            // Update transaction with PayOS response
            $transaction->update([
                'payos_payment_link_id' => $response['paymentLinkId'] ?? null,
                'payos_response' => $response
            ]);

            Log::info('PayOS payment link created', [
                'user_id' => $user->id,
                'order_code' => $orderCode,
                'package_type' => $packageType,
                'response' => $response
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'checkoutUrl' => $response['checkoutUrl'],
                    'orderCode' => $orderCode,
                    'amount' => $packageConfig['amount'],
                    'tokens' => $packageConfig['tokens'],
                    'package' => $packageConfig
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('PayOS payment link creation failed', [
                'user_id' => Auth::id(),
                'package_type' => $packageType ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create payment link. Please try again.'
            ], 500);
        }
    }

    /**
     * Get payment transaction status
     */
    public function getPaymentStatus(Request $request, $orderCode)
    {
        try {
            $user = Auth::user();
            $transaction = PaymentTransaction::where('payos_order_code', $orderCode)
                ->where('user_id', $user->id)
                ->first();

            if (!$transaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction not found'
                ], 404);
            }

            // Get latest status from PayOS
            $payosResponse = $this->payOS->getPaymentLinkInformation($orderCode);

            // Update local transaction status if needed
            if (isset($payosResponse['status'])) {
                $this->updateTransactionStatus($transaction, $payosResponse);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'orderCode' => $transaction->payos_order_code,
                    'status' => $transaction->status,
                    'amount' => $transaction->amount,
                    'tokens' => $transaction->tokens,
                    'package_type' => $transaction->package_type,
                    'created_at' => $transaction->created_at,
                    'paid_at' => $transaction->paid_at
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('PayOS payment status check failed', [
                'user_id' => Auth::id(),
                'order_code' => $orderCode,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get payment status'
            ], 500);
        }
    }

    /**
     * Handle PayOS webhook
     */
    public function handleWebhook(Request $request)
    {
        try {
            $webhookData = $request->all();

            Log::info('PayOS webhook received', ['data' => $webhookData]);

            // Verify webhook signature
            if (!$this->verifyWebhookSignature($request)) {
                Log::warning('PayOS webhook signature verification failed');
                return response()->json(['message' => 'Invalid signature'], 400);
            }

            $orderCode = $webhookData['data']['orderCode'] ?? null;
            if (!$orderCode) {
                Log::warning('PayOS webhook missing order code');
                return response()->json(['message' => 'Missing order code'], 400);
            }

            $transaction = PaymentTransaction::where('payos_order_code', $orderCode)->first();
            if (!$transaction) {
                Log::warning('PayOS webhook transaction not found', ['order_code' => $orderCode]);
                return response()->json(['message' => 'Transaction not found'], 404);
            }

            // Update transaction status based on webhook data
            $this->updateTransactionStatus($transaction, $webhookData['data']);

            return response()->json(['message' => 'Webhook processed successfully']);

        } catch (\Exception $e) {
            Log::error('PayOS webhook processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json(['message' => 'Webhook processing failed'], 500);
        }
    }

    /**
     * Cancel payment transaction
     */
    public function cancelPayment(Request $request, $orderCode)
    {
        try {
            $user = Auth::user();
            $transaction = PaymentTransaction::where('payos_order_code', $orderCode)
                ->where('user_id', $user->id)
                ->where('status', PaymentTransaction::STATUS_PENDING)
                ->first();

            if (!$transaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction not found or cannot be cancelled'
                ], 404);
            }

            // Cancel payment link in PayOS
            $cancellationReason = $request->input('reason', 'Cancelled by user');

            $this->payOS->cancelPaymentLink($orderCode, $cancellationReason);

            // Update local transaction
            $transaction->markAsCancelled($cancellationReason);

            Log::info('PayOS payment cancelled', [
                'user_id' => $user->id,
                'order_code' => $orderCode,
                'reason' => $cancellationReason
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment cancelled successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('PayOS payment cancellation failed', [
                'user_id' => Auth::id(),
                'order_code' => $orderCode,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel payment'
            ], 500);
        }
    }

    /**
     * Update transaction status based on PayOS response
     */
    private function updateTransactionStatus(PaymentTransaction $transaction, array $payosData)
    {
        $status = $payosData['status'] ?? null;

        if ($status === 'PAID' && !$transaction->isPaid()) {
            $transaction->markAsPaid();

            // Add tokens to user account
            $user = $transaction->user;
            $user->tokens += $transaction->tokens;
            $user->save();

            Log::info('PayOS payment completed and tokens added', [
                'user_id' => $user->id,
                'order_code' => $transaction->payos_order_code,
                'tokens_added' => $transaction->tokens,
                'new_token_balance' => $user->tokens
            ]);
        } elseif ($status === 'CANCELLED' && !$transaction->isCancelled()) {
            $reason = $payosData['cancellationReason'] ?? 'Cancelled via PayOS';
            $transaction->markAsCancelled($reason);
        }

        // Update PayOS response data
        $transaction->update(['payos_response' => $payosData]);
    }

    /**
     * Verify webhook signature
     */
    private function verifyWebhookSignature(Request $request)
    {
        $signature = $request->header('X-PayOS-Signature');
        if (!$signature) {
            return false;
        }

        $webhookData = $request->getContent();
        $expectedSignature = hash_hmac('sha256', $webhookData, config('payos.checksum_key'));

        return hash_equals($expectedSignature, $signature);
    }
}
