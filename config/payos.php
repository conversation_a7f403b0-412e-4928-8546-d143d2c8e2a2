<?php

return [
    /*
    |--------------------------------------------------------------------------
    | PayOS Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for PayOS payment gateway integration
    |
    */

    'client_id' => env('PAYOS_CLIENT_ID'),
    'api_key' => env('PAYOS_API_KEY'),
    'checksum_key' => env('PAYOS_CHECKSUM_KEY'),
    'partner_code' => env('PAYOS_PARTNER_CODE'),
    'environment' => env('PAYOS_ENVIRONMENT', 'sandbox'),

    /*
    |--------------------------------------------------------------------------
    | PayOS URLs
    |--------------------------------------------------------------------------
    */

    'return_url' => env('APP_URL') . '/payment/success',
    'cancel_url' => env('APP_URL') . '/payment/cancel',
    'webhook_url' => env('APP_URL') . '/api/payos/webhook',

    /*
    |--------------------------------------------------------------------------
    | Token Packages
    |--------------------------------------------------------------------------
    |
    | Define the available token packages for purchase
    |
    */

    'packages' => [
        'basic' => [
            'name' => 'Basic',
            'tokens' => 5,
            'price' => 4.99,
            'amount' => 499, // Amount in cents for PayOS
            'description' => 'Perfect for occasional exports'
        ],
        'standard' => [
            'name' => 'Standard',
            'tokens' => 15,
            'price' => 9.99,
            'amount' => 999, // Amount in cents for PayOS
            'description' => 'Most popular option'
        ],
        'premium' => [
            'name' => 'Premium',
            'tokens' => 30,
            'price' => 14.99,
            'amount' => 1499, // Amount in cents for PayOS
            'description' => 'Best value for active users'
        ]
    ]
];
